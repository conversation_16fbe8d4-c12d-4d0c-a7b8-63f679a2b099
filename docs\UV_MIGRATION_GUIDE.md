# uv包管理器迁移指南

## 概述

本项目已成功从传统的pip/requirements.txt依赖管理方式迁移到使用uv包管理器。本文档详细说明了迁移过程、变更内容以及如何使用新的依赖管理系统。

## 迁移前后对比

### 迁移前
- 使用 `requirements.txt` 管理依赖
- 使用 `pip` 安装包
- 手动管理虚拟环境
- 没有依赖锁定文件

### 迁移后
- 使用 `pyproject.toml` 定义项目配置和依赖
- 使用 `uv.lock` 锁定精确的依赖版本
- 使用 `uv` 进行快速的包管理
- 自动管理虚拟环境
- 保留 `requirements.txt` 作为备份

## 文件变更

### 新增文件
- `pyproject.toml` - 项目配置文件，包含依赖定义、构建配置、工具配置
- `uv.lock` - 依赖锁定文件，确保环境一致性
- `docs/UV_MIGRATION_GUIDE.md` - 本迁移指南

### 修改文件
- `Dockerfile` - 更新为使用uv进行依赖安装
- `README.md` - 添加uv使用说明

### 保留文件
- `requirements.txt` - 保留作为备份，但推荐使用uv

## 依赖项迁移详情

### 主要依赖类别

1. **Web框架和API**
   - Django 5.1.3
   - Django REST Framework 3.15.2
   - 相关扩展包

2. **数据库和缓存**
   - psycopg2-binary 2.9.10
   - django-redis 6.0.0
   - redis 6.2.0

3. **异步和任务队列**
   - channels 4.2.0
   - django-q2 1.7.6

4. **数据处理**
   - pandas 2.2.3
   - numpy 2.1.3
   - matplotlib 3.9.2

5. **云服务集成**
   - django5-aliyun-oss 1.1.2
   - alibabacloud-dysmsapi20170525 4.1.1
   - openai 1.59.3

### 版本兼容性

所有依赖项的版本都保持与原requirements.txt兼容，确保项目功能不受影响。

## 使用指南

### 开发环境设置

```bash
# 1. 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 克隆项目
git clone [项目地址]
cd Backend

# 3. 同步依赖（自动创建虚拟环境）
uv sync

# 4. 运行项目
uv run python manage.py runserver
```

### 常用命令

#### 依赖管理
```bash
# 添加生产依赖
uv add django-extensions

# 添加开发依赖
uv add --dev pytest-django

# 移除依赖
uv remove package-name

# 更新依赖
uv sync

# 查看依赖树
uv tree
```

#### 运行命令
```bash
# Django管理命令
uv run python manage.py migrate
uv run python manage.py collectstatic
uv run python manage.py createsuperuser

# 运行测试
uv run pytest

# 启动开发服务器
uv run python manage.py runserver
```

#### 环境管理
```bash
# 同步所有依赖（包括dev）
uv sync

# 只同步生产依赖
uv sync --no-dev

# 锁定依赖版本
uv lock

# 导出requirements.txt（如果需要）
uv export --format requirements-txt --output-file requirements.txt
```

## Docker部署

Dockerfile已更新为使用uv：

```dockerfile
# 安装uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装依赖
RUN uv sync --frozen --no-dev

# 运行Django命令
RUN uv run python manage.py collectstatic --noinput
```

## 性能优势

### 安装速度对比
- **pip**: 传统安装方式，速度较慢
- **uv**: 比pip快10-100倍，特别是在大型项目中

### 依赖解析
- **pip**: 可能出现依赖冲突
- **uv**: 更智能的依赖解析，避免冲突

### 缓存机制
- **pip**: 基本缓存
- **uv**: 高效的全局缓存，减少重复下载

## 故障排除

### 常见问题

1. **uv命令未找到**
   ```bash
   # 确保uv已正确安装并在PATH中
   curl -LsSf https://astral.sh/uv/install.sh | sh
   source ~/.bashrc  # 或重启终端
   ```

2. **依赖冲突**
   ```bash
   # 清除缓存并重新同步
   uv cache clean
   uv sync --refresh
   ```

3. **虚拟环境问题**
   ```bash
   # 删除现有环境并重新创建
   rm -rf .venv
   uv sync
   ```

### 回退到pip（如果需要）

如果遇到问题需要回退到pip：

```bash
# 1. 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 2. 使用requirements.txt安装
pip install -r requirements.txt
```

## 最佳实践

1. **定期更新依赖**
   ```bash
   uv sync --upgrade
   ```

2. **提交锁定文件**
   - 始终将 `uv.lock` 提交到版本控制
   - 确保团队环境一致性

3. **使用开发依赖**
   ```bash
   # 将测试和开发工具添加为dev依赖
   uv add --dev pytest black isort mypy
   ```

4. **环境隔离**
   - uv自动管理虚拟环境
   - 每个项目都有独立的环境

## 总结

迁移到uv包管理器为项目带来了以下好处：

- ✅ 更快的依赖安装和解析
- ✅ 更好的依赖冲突解决
- ✅ 统一的项目配置管理
- ✅ 自动的虚拟环境管理
- ✅ 锁定文件确保环境一致性
- ✅ 向后兼容，保留requirements.txt

项目的所有功能都经过验证，确保迁移过程中没有破坏性变更。开发团队可以立即开始使用uv进行日常开发工作。
