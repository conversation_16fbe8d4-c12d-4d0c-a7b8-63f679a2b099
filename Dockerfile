# 基础镜像，使用官方的python镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置Debian国内镜像源
RUN echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye-backports main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian-security bullseye-security main contrib non-free" >> /etc/apt/sources.list

# 安装系统依赖和curl（用于安装uv）
RUN apt-get update && apt-get install -y \
    gcc \
    supervisor \
    sudo \
    wkhtmltopdf \
    fonts-noto-cjk \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# 创建非特权用户，并创建家目录，配置sudo权限
RUN groupadd -r appuser && useradd -r -g appuser -s /bin/bash -m appuser \
    && usermod -aG root appuser \
    && echo "appuser ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# 创建必要的目录并设置权限
RUN mkdir -p /app/media /app/static /app/logs /var/log \
    && chmod -R 755 /app/media /app/static \
    && chmod -R 777 /app/logs /var/log \
    && chown -R appuser:appuser /app/media /app/static /app/logs

# 设置环境变量
ENV DJANGO_ENV=product \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    STATIC_ROOT=/app/static \
    MEDIA_ROOT=/app/media

# 复制项目的依赖文件
COPY pyproject.toml uv.lock ./

# 设置uv使用阿里云镜像
ENV UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/

# 使用uv安装依赖
RUN uv sync --frozen --no-dev

# 复制整个项目到工作目录
COPY . .

# 复制supervisor配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 复制启动脚本并确保正确的行结束符
COPY entrypoint.sh /app/entrypoint.sh
RUN sed -i 's/\r$//' /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# 生成静态文件
RUN uv run python manage.py collectstatic --noinput \
    && chmod -R 755 /app/static \
    && chown -R appuser:appuser /app/static

# 设置权限
RUN chown -R appuser:appuser /app \
    && chmod +x /app/entrypoint.sh

# 暴露端口
EXPOSE 8001 9002

# 注意：不在这里切换用户，在entrypoint.sh中处理用户切换
# 使用启动脚本
CMD ["/app/entrypoint.sh"]
