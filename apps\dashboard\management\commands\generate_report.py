from django.core.management.base import BaseCommand
from django.utils import timezone
from django.template.loader import render_to_string
from django.conf import settings
import os
import json
from datetime import datetime
from apps.exercise.models import Exercise
from apps.infection.models import InfectionRecord, DataLeakage

class Command(BaseCommand):
    help = '生成演练报告'

    def add_arguments(self, parser):
        parser.add_argument(
            'exercise_id',
            type=int,
            help='演练ID'
        )
        parser.add_argument(
            '--format',
            choices=['json', 'html', 'txt'],
            default='json',
            help='报告格式（默认：json）'
        )
        parser.add_argument(
            '--output',
            help='输出文件路径'
        )

    def handle(self, *args, **options):
        try:
            exercise = Exercise.objects.get(id=options['exercise_id'])
            
            # 收集演练数据
            report_data = self.collect_exercise_data(exercise)
            
            # 根据格式生成报告
            if options['format'] == 'json':
                content = json.dumps(report_data, ensure_ascii=False, indent=2)
                extension = 'json'
            elif options['format'] == 'html':
                content = render_to_string('dashboard/exercise_report.html', report_data)
                extension = 'html'
            else:  # txt
                content = self.generate_text_report(report_data)
                extension = 'txt'
            
            # 确定输出路径
            if options['output']:
                output_path = options['output']
            else:
                filename = f"exercise_report_{exercise.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{extension}"
                output_path = os.path.join(settings.MEDIA_ROOT, 'reports', filename)
                
            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.stdout.write(
                self.style.SUCCESS(f'报告已生成: {output_path}')
            )
            
        except Exercise.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'演练ID {options["exercise_id"]} 不存在')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'生成报告时出错: {str(e)}')
            )

    def collect_exercise_data(self, exercise):
        """收集演练数据"""
        # 获取感染记录
        infection_records = InfectionRecord.objects.filter(exercise=exercise)
        
        # 获取数据泄露记录
        data_leakages = DataLeakage.objects.filter(
            infection_record__exercise=exercise
        )
        
        # 计算统计数据
        stats = {
            'total_assets': (
                exercise.target_assets.count() +
                sum(group.assets.count() for group in exercise.target_groups.all())
            ),
            'infected_count': infection_records.count(),
            'encrypted_files': infection_records.aggregate(
                total=Sum('encrypted_files')
            )['total'] or 0,
            'total_data_loss': infection_records.aggregate(
                total=Sum('data_loss')
            )['total'] or 0
        }
        
        # 构建报告数据
        return {
            'exercise': {
                'id': exercise.id,
                'name': exercise.name,
                'description': exercise.description,
                'virus_name': exercise.virus.name,
                'start_time': exercise.start_time.isoformat(),
                'end_time': exercise.end_time.isoformat() if exercise.end_time else None,
                'status': exercise.get_status_display(),
                'duration': str(exercise.end_time - exercise.start_time) if exercise.end_time else None
            },
            'statistics': stats,
            'infection_records': [
                {
                    'asset_name': record.asset.name,
                    'ip_address': record.asset.ip_address,
                    'department': record.asset.department,
                    'status': record.get_status_display(),
                    'infection_time': record.infection_time.isoformat(),
                    'encrypted_files': record.encrypted_files,
                    'data_loss': record.data_loss
                }
                for record in infection_records
            ],
            'data_leakages': [
                {
                    'type': leak.get_leakage_type_display(),
                    'file_count': leak.file_count,
                    'data_size': leak.data_size,
                    'sensitivity': leak.sensitivity,
                    'description': leak.description
                }
                for leak in data_leakages
            ],
            'generated_at': timezone.now().isoformat()
        }

    def generate_text_report(self, data):
        """生成文本格式的报告"""
        report = [
            f"演练报告 - {data['exercise']['name']}",
            "=" * 50,
            f"\n基本信息：",
            f"演练ID：{data['exercise']['id']}",
            f"描述：{data['exercise']['description']}",
            f"使用病毒：{data['exercise']['virus_name']}",
            f"开始时间：{data['exercise']['start_time']}",
            f"结束时间：{data['exercise']['end_time']}",
            f"状态：{data['exercise']['status']}",
            f"持续时间：{data['exercise']['duration']}",
            
            f"\n统计数据：",
            f"目标资产数：{data['statistics']['total_assets']}",
            f"感染资产数：{data['statistics']['infected_count']}",
            f"加密文件数：{data['statistics']['encrypted_files']}",
            f"数据丢失量：{data['statistics']['total_data_loss']} 字节",
            
            f"\n感染记录：",
            "=" * 30
        ]
        
        for record in data['infection_records']:
            report.extend([
                f"\n资产：{record['asset_name']} ({record['ip_address']})",
                f"部门：{record['department']}",
                f"状态：{record['status']}",
                f"感染时间：{record['infection_time']}",
                f"加密文件：{record['encrypted_files']}",
                f"数据丢失：{record['data_loss']} 字节",
                "-" * 20
            ])
        
        if data['data_leakages']:
            report.extend([
                f"\n数据泄露情况：",
                "=" * 30
            ])
            
            for leak in data['data_leakages']:
                report.extend([
                    f"\n类型：{leak['type']}",
                    f"文件数：{leak['file_count']}",
                    f"数据量：{leak['data_size']} 字节",
                    f"敏感度：{leak['sensitivity']}",
                    f"描述：{leak['description']}",
                    "-" * 20
                ])
        
        report.extend([
            f"\n报告生成时间：{data['generated_at']}",
            "=" * 50
        ])
        
        return "\n".join(report) 