from django.utils import timezone
from django.template import Template, Context
from django.core.mail import send_mail
from django.conf import settings
from .models import PhishingRecord
from apps.exercise.models import ExerciseLog
from apps.system.services import EmailService
import uuid
import json

class PhishingService:
    """钓鱼邮件服务类
    
    处理钓鱼邮件的发送和追踪
    """
    
    @staticmethod
    def generate_tracking_id():
        """生成追踪ID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def create_tracking_url(tracking_id, exercise_id):
        """创建追踪链接"""
        base_url = settings.PHISHING_TRACK_URL
        return f"{base_url}?tid={tracking_id}&eid={exercise_id}"
    
    @classmethod
    def send_phishing_email(cls, exercise, template_name, recipients, **context):
        """发送钓鱼邮件
        
        Args:
            exercise: 演练对象
            template_name: 邮件模板名称
            recipients: 收件人列表
            **context: 模板上下文数据
        """
        try:
            # 获取邮件模板
            template = EmailService.get_email_template(template_name)
            if not template:
                raise ValueError(f"邮件模板 {template_name} 不存在")
            
            # 为每个收件人生成唯一的追踪ID
            for recipient in recipients:
                tracking_id = cls.generate_tracking_id()
                tracking_url = cls.create_tracking_url(tracking_id, exercise.id)
                
                # 准备模板上下文
                email_context = {
                    **context,
                    'tracking_url': tracking_url,
                    'recipient_email': recipient
                }
                
                # 渲染邮件内容
                content = Template(template.content).render(Context(email_context))
                
                # 发送邮件
                send_mail(
                    subject=template.subject,
                    message=content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[recipient],
                    fail_silently=False
                )
                
                # 记录发送日志
                ExerciseLog.objects.create(
                    exercise=exercise,
                    event_type='PH',  # 钓鱼事件
                    description=f'发送钓鱼邮件到 {recipient}',
                    metadata={
                        'tracking_id': tracking_id,
                        'template': template_name,
                        'recipient': recipient
                    }
                )
                
            return True
            
        except Exception as e:
            # 记录错误日志
            ExerciseLog.objects.create(
                exercise=exercise,
                event_type='ER',
                description=f'发送钓鱼邮件失败: {str(e)}'
            )
            return False
    
    @classmethod
    def record_click(cls, tracking_id, request):
        """记录钓鱼链接点击
        
        Args:
            tracking_id: 追踪ID
            request: HTTP请求对象
        """
        try:
            # 获取相关日志记录
            log = ExerciseLog.objects.get(
                metadata__tracking_id=tracking_id,
                event_type='PH'
            )
            
            # 创建钓鱼记录
            PhishingRecord.objects.create(
                exercise=log.exercise,
                user=request.user if request.user.is_authenticated else None,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                referrer=request.META.get('HTTP_REFERER', ''),
                email_template=log.metadata.get('template'),
                metadata={
                    'tracking_id': tracking_id,
                    'headers': dict(request.headers),
                    'timestamp': timezone.now().isoformat()
                }
            )
            
            # 更新演练日志
            ExerciseLog.objects.create(
                exercise=log.exercise,
                event_type='PH',
                description=f'用户点击钓鱼链接 {tracking_id}',
                metadata={
                    'tracking_id': tracking_id,
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')
                }
            )
            
            return True
            
        except ExerciseLog.DoesNotExist:
            return False
        except Exception as e:
            # 记录错误但不影响用户体验
            print(f"记录钓鱼点击失败: {str(e)}")
            return False
    
    @staticmethod
    def get_phishing_statistics(exercise_id=None, start_date=None, end_date=None):
        """获取钓鱼统计数据
        
        Args:
            exercise_id: 演练ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
        """
        queryset = PhishingRecord.objects.all()
        
        if exercise_id:
            queryset = queryset.filter(exercise_id=exercise_id)
        if start_date:
            queryset = queryset.filter(click_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(click_time__lte=end_date)
        
        # 计算统计数据
        stats = {
            'total_clicks': queryset.count(),
            'unique_users': queryset.values('user').distinct().count(),
            'unique_ips': queryset.values('ip_address').distinct().count(),
            'infection_rate': queryset.filter(is_infected=True).count() / queryset.count() * 100 if queryset.exists() else 0,
            'browser_distribution': dict(
                queryset.values('user_agent')
                .annotate(count=Count('id'))
                .values_list('user_agent', 'count')
            ),
            'hourly_distribution': dict(
                queryset.annotate(hour=ExtractHour('click_time'))
                .values('hour')
                .annotate(count=Count('id'))
                .values_list('hour', 'count')
            )
        }
        
        return stats 