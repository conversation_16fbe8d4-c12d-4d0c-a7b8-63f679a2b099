[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ransomware-simulation-platform"
version = "1.0.0"
description = "勒索病毒模拟演练平台后端服务"
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
authors = [
    {name = "Development Team", email = "<EMAIL>"},
]
keywords = ["django", "ransomware", "simulation", "security", "platform"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Framework :: Django",
    "Framework :: Django :: 5.1",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Security",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    # Web框架和API
    "django==5.1.3",
    "djangorestframework==3.15.2",
    "django-cors-headers==4.6.0",
    "django-filter==24.3",
    "drf-spectacular==0.27.2",
    "drf-nested-routers==0.94.1",
    "drf-writable-nested==0.7.1",
    "drf-access-policy==1.5.0",
    "django-rest-framework-nested==0.0.1",

    # 认证和安全
    "djangorestframework-simplejwt==5.3.1",
    "djoser==2.3.1",
    "pyotp==2.9.0",
    "social-auth-app-django==5.4.2",

    # 数据库和缓存
    "psycopg2-binary==2.9.10",
    "django-redis>=5.2.0",
    "redis>=4.5.4",

    # 异步和任务队列
    "channels==4.2.0",
    "django-q2==1.7.6",

    # 数据处理和分析
    "pandas==2.2.3",
    "numpy==2.1.3",
    "matplotlib==3.9.2",
    "openpyxl==2.4.11",

    # 云服务和第三方API
    "django5-aliyun-oss==1.1.2",
    "alibabacloud-dysmsapi20170525>=2.0.24",
    "openai==1.59.3",
    "cozepy==0.9.1",

    # HTTP客户端
    "requests==2.32.3",
    "httpx==0.27.0",

    # 文档处理
    "python-docx==1.1.2",
    "pdfkit==1.0.0",

    # 部署和服务器
    "gunicorn==21.2.0",
    "whitenoise==6.8.2",

    # 日志和监控
    "loguru==0.7.3",
    "psutil==6.1.0",

    # 模板和工具
    "jinja2>=3.1.6",
    "user-agents>=2.2.0",

    # 配置和环境
    "python-dotenv==1.0.1",
    "pyyaml==6.0.2",
]

[tool.hatch.build.targets.wheel]
packages = ["apps", "config", "extensions"]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-django",
    "pytest-cov",
    "black",
    "isort",
    "flake8",
    "mypy",
]

test = [
    "pytest",
    "pytest-django",
    "pytest-cov",
    "factory-boy",
    "faker",
]

docs = [
    "sphinx",
    "sphinx-rtd-theme",
    "sphinx-autodoc-typehints",
]

[project.urls]
Homepage = "https://github.com/your-org/ransomware-simulation-platform"
Documentation = "https://your-docs-url.com"
Repository = "https://github.com/your-org/ransomware-simulation-platform"
"Bug Tracker" = "https://github.com/your-org/ransomware-simulation-platform/issues"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-django>=4.5.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_django = "django"
known_first_party = ["apps", "config", "extensions"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "DJANGO", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.10"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "config.settings"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
addopts = "--cov=apps --cov-report=html --cov-report=term-missing"
testpaths = ["apps"]

[tool.coverage.run]
source = ["apps"]
omit = [
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "manage.py",
    "config/wsgi.py",
    "config/asgi.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]
