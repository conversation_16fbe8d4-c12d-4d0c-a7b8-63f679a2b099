import io
from datetime import timedelta
from urllib import parse

from django.db.models import Count
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from docx import Document
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
# from docx.opc.oxml import qn
from docx.oxml.ns import qn
from docx.shared import Pt
from drf_spectacular.utils import extend_schema, extend_schema_view
from jinja2 import Template
from rest_framework import status, mixins, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_q.tasks import async_task, schedule
from django_q.models import Schedule, Task
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from extensions import permissions_utils, queryset_utils, constants
from .filters import ExerciseFilter
from .models import Exercise
from .serializers import (
    ExerciseSerializer
)
from ..assets.models import Asset, AssetGroup
from apps.infection.models import InfectionRecord, Device
from ..phishing.models import EmailLog, FormSubmitModel
from ..system.models import NotifyModel


@extend_schema_view(
    list=extend_schema(
        summary="获取演练列表",
        description="获取所有演练项目列表，所有已认证用户可访问",
        tags=["演练管理"]
    ),
    create=extend_schema(
        summary="创建演练",
        description="创建新的演练项目，仅管理员可操作",
        tags=["演练管理"]
    ),
    retrieve=extend_schema(
        summary="获取演练详情",
        description="获取指定演练项目的详细信息，所有已认证用户可访问",
        tags=["演练管理"]
    ),
    update=extend_schema(
        summary="更新演练",
        description="更新指定演练项目的信息，仅管理员可操作",
        tags=["演练管理"]
    ),
    partial_update=extend_schema(
        summary="部分更新演练",
        description="部分更新指定演练项目的信息，仅管理员可操作",
        tags=["演练管理"]
    ),
    destroy=extend_schema(
        summary="删除演练",
        description="删除指定演练项目，仅管理员可操作",
        tags=["演练管理"]
    )
)
class ExerciseViewSet(queryset_utils.UserOwnedModelViewSet):
    """演练项目管理视图集"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    @extend_schema(
        summary="开始演练",
        description="启动指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'},
                    'message': {'type': 'string', 'description': '提示信息'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """开始演练"""
        exercise = self.get_object()
        now = timezone.now()

        # 检查演练状态
        if exercise.status != Exercise.Status.PENDING:
            return Response(
                {'error': '只有待开始状态的演练可以启动'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 检查结束时间
        if exercise.end_time < now:
            return Response(
                {'error': '演练已结束,不能进行演练'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            'status': exercise.get_status_display(),
            'message': f'演练将在 {exercise.start_time} 开始执行'
        })
        # 如果已经有定时任务ID,先取消之前的任务
        # if exercise.task_id:
        #     Schedule.objects.filter(id=exercise.task_id).delete()
        #     exercise.task_id = None
        #     exercise.save()

        # 判断是否需要立即开始还是定时执行
        # if exercise.start_time <= now:
        #     # 立即执行
        #     task = async_task(
        #         'apps.exercise.tasks.send_exercise_emails',
        #         exercise.id,
        #         name=f'exercise_send_email_{exercise.id}'
        #     )
        #     exercise.task_id = task
        #     exercise.save()
        #     return Response({
        #         'status': exercise.get_status_display(),
        #         'message': '演练任务已开始执行'
        #     })
        # else:
        #     # 创建定时任务
        #     task = schedule(
        #         'apps.exercise.tasks.send_exercise_emails',
        #         exercise.id,
        #         schedule_type=Schedule.ONCE,
        #         next_run=exercise.start_time,
        #         name=f'exercise_send_email_{exercise.id}'
        #     )
        #     exercise.task_id = task.id
        #     exercise.save()
        #     return Response({
        #         'status': exercise.get_status_display(),
        #         'message': f'演练将在 {exercise.start_time} 开始执行'
        #     })

    @extend_schema(
        summary="暂停演练",
        description="暂停指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """暂停演练"""
        exercise = self.get_object()
        if exercise.status != Exercise.Status.RUNNING:
            return Response(
                {'error': '只有进行中的演练可以暂停'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.PAUSED
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="结束演练",
        description="结束指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def finish(self, request, pk=None):
        """结束演练"""
        exercise = self.get_object()
        if exercise.status not in [Exercise.Status.RUNNING, Exercise.Status.PAUSED]:
            return Response(
                {'error': '只有进行中或已暂停的演练可以完成'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.FINISHED
        exercise.end_time = timezone.now()
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="终止演练",
        description="强制终止指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def terminate(self, request, pk=None):
        """终止演练"""
        exercise = self.get_object()
        if exercise.status in [Exercise.Status.FINISHED, Exercise.Status.TERMINATED]:
            return Response(
                {'error': '已完成或已终止的演练不能终止'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.TERMINATED
        exercise.end_time = timezone.now()
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="获取演练统计信息",
        description="获取指定演练项目的统计信息,包括目标设备总数、已感染设备数、钓鱼邮件统计等",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'total_devices': {'type': 'integer', 'description': '目标设备总数'},
                    'infected_devices': {'type': 'integer', 'description': '已感染设备数'},
                    'phishing_rate': {'type': 'number', 'description': '钓鱼邮件点击率'},
                    'total_emails': {'type': 'integer', 'description': '发送邮件总数'},
                    'clicked_emails': {'type': 'integer', 'description': '已点击邮件数'},
                    'timeline': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'event': {'type': 'string', 'description': '事件描述'},
                                'datetime': {'type': 'string', 'description': '事件时间'},
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取演练统计信息"""
        exercise = self.get_object()

        # 获取演练目标资产组中的所有资产
        target_assets = Asset.objects.filter(group__in=exercise.target_groups.all())

        # 获取终端和邮件资产
        devices = target_assets.filter(asset_type__in=['EP', 'SV'])
        emails = target_assets.filter(asset_type='EM')

        # 获取感染记录
        infection_records = InfectionRecord.objects.filter(
            asset__in=devices,
            created_at__range=(exercise.start_time, exercise.end_time or timezone.now())
        )

        # 统计感染设备
        infected_devices = infection_records.values('asset').distinct().count()

        # 统计邮件点击
        clicked_emails = emails.filter(status='clicked').count()

        # 构建时间轴
        timeline = []

        # 添加开始事件
        if exercise.start_time:
            timeline.append({
                'event': '演练开始',
                'datetime': exercise.start_time.isoformat()
            })

        # 添加感染事件
        for record in infection_records.order_by('created_at'):
            timeline.append({
                'event': f'设备 {record.asset.name} 被感染',
                'datetime': record.created_at.isoformat()
            })

        # 添加结束事件
        if exercise.end_time:
            timeline.append({
                'event': '演练结束',
                'datetime': exercise.end_time.isoformat()
            })

        return Response({
            'total_devices': devices.count(),
            'infected_devices': infected_devices,
            'total_emails': emails.count(),
            'clicked_emails': clicked_emails,
            'phishing_rate': round(clicked_emails / emails.count() * 100 if emails.count() > 0 else 0, 2),
            'timeline': timeline
        })

    @action(detail=True, methods=['get'])
    def department(self, request, pk=None):
        exercise = self.get_object()
        target_assets = exercise.target_asset.all()
        department_data = []
        for group in target_assets:
            # 获取该邮件组下所有资产的邮箱地址
            assets_in_group = group.asset.filter(asset_type='EM').all()
            email_count = assets_in_group.filter(email__isnull=False).count()  # 统计有效邮箱
            # 存储邮件组名和邮箱数
            department_data.append({
                'name': group.name,
                'value': email_count,
            })

        # 返回结果
        return Response({
            'exercise_id': exercise.id,
            'department_data': department_data
        })

    @action(detail=True, methods=['get'])
    def phishing_email_sending_overview(self, request, pk=None):
        """
        钓鱼邮件发送总览
        """
        exercise = self.get_object()
        email_logs = EmailLog.objects.filter(exercise=exercise)
        return Response({
            "count": email_logs.count(),
            "success_count": email_logs.filter(status="SUCCESS").count(),
            "failed_count": email_logs.exclude(status="SUCCESS").count(),
        })

    @action(detail=True, methods=['get'])
    def open_email_data_statistics(self, request, pk=None):
        """
        打开邮件数据统计
        """
        exercise = self.get_object()

        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        logs = EmailLog.objects.filter(exercise=exercise)

        # 总打开数
        total_open = logs.filter(is_click=True).count()

        # 今日打开
        today_open = logs.filter(is_click=True, updated_at__date=today).count()

        # 昨日打开
        yesterday_open = logs.filter(is_click=True, updated_at__date=yesterday).count()

        # 环比增长率
        if yesterday_open == 0:
            increase_rate = 100.0 if today_open > 0 else 0.0
        else:
            increase_rate = ((today_open - yesterday_open) / yesterday_open) * 100

        return Response({
            "total_open": total_open,
            "today_open": today_open,
            "yesterday_open": yesterday_open,
            "increase_rate": round(increase_rate, 2)
        })

    @action(detail=True, methods=['get'])
    def phishing_email_click_stats(self, request, pk=None):
        """
        钓鱼邮件点击情况
        """
        exercise = self.get_object()

        logs = EmailLog.objects.filter(exercise=exercise)
        form_datas = FormSubmitModel.objects.filter(email_log__in=logs)
        # 总打开数
        total_open = logs.filter(is_click=True).count()
        # 链接点击数
        link_click = form_datas.filter(status="CLICK")
        # 表单提交数
        form = form_datas.filter(status="SUBMIT")

        return Response({
            "total_open": total_open,
            "link_click_count": link_click.count(),
            "form_count": form.count(),
        })

    @action(detail=True, methods=['get'])
    def infection_overview(self, request, pk=None):
        """
        感染总览
        """
        exercise = self.get_object()
        target_groups = exercise.target_asset.all()
        target_assets = Asset.objects.filter(group__in=target_groups)
        # 获取终端和邮件资产
        devices = target_assets.filter(asset_type__in=['EP', 'SV'])
        infected_count = Device.objects.filter(
            exercise=exercise,
            infection_count__gt=0
        ).count()
        return Response({
            "assets_count": devices.count(),
            "infected_count": infected_count,
            "not_infected_count": devices.count() - infected_count
        })

    @action(detail=True, methods=['get'])
    def device_statistics(self, request, pk=None):
        # 获取当前演练任务对象
        exercise = self.get_object()

        # 获取所有目标资产组
        target_groups = exercise.target_groups.all()

        # 获取所有目标资产组内的所有资产
        assets = Asset.objects.filter(group__in=target_groups)

        # 统计终端设备和服务器设备的数量
        terminal_count = assets.filter(asset_type='EP').count()  # 终端设备（EP）
        server_count = assets.filter(asset_type='SV').count()  # 服务器设备（SV）

        # 返回统计结果
        return Response([
            {"name": "服务器设备", "value": server_count},
            {"name": "终端设备", "value": terminal_count},
        ])

    @action(detail=True, methods=['get'])
    def infection_statistics(self, request, pk=None):
        """
        获取某个演练下，各资产组的感染设备数量
        """
        # 获取演练对象
        exercise = self.get_object()

        # 获取该演练下的所有目标资产组
        target_groups = exercise.target_groups.all()

        # 统计每个资产组下的感染设备数量
        result = []
        for group in target_groups:
            # 获取该资产组下的所有设备
            infected_count = Device.objects.filter(
                exercise=exercise,
                # device_id__in=[str(asset_id) for asset_id in group.asset.values_list('id', flat=True)],
                # infection_count__gt=0  # 过滤掉感染次数为0的设备
            ).count()

            # 组装结果
            result.append({
                "name": group.name,
                "value": infected_count
            })

        return Response(result)

    @action(detail=False, methods=['get'], permission_classes=[], authentication_classes=[])
    def reserve_email(self, request, pk=None):
        """
        接收邮件
        """
        email = request.query_params.get("email")
        exercise_id = request.query_params.get("exercise_id")
        if not (email and exercise_id):
            return Response({"msg": "缺失必要参数"})
        from loguru import logger
        logger.info(f"email,exercise_id============{email},{exercise_id}=====================")
        email_log_objs = EmailLog.objects.filter(recipient=email, exercise_id=exercise_id)
        email_log_objs.update(
            is_click=True
        )
        exercise = get_object_or_404(Exercise, id=exercise_id)
        NotifyModel.objects.create(
            exercise_id=exercise_id,
            user=exercise.created_by,
            content=f"{exercise.name}演练下的{email}打开了邮件"
        )
        return Response({"msg": f"更新成功{email_log_objs.count()}"})

    @action(detail=True, methods=['get'])
    def send_email_records(self, request, pk=None):
        """邮件发送记录"""
        exercise = self.get_object()
        email_logs = EmailLog.objects.filter(exercise=exercise)
        data = []
        for row in email_logs:
            data.append({
                "recipient": row.recipient,
                "email_subject": row.email_task.email_template.email_subject,
                "send_time": row.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "is_click": row.is_click,
            })
        return Response({"results": data})


class ExerciseDashboardStatisticsViewSet(queryset_utils.UserOwnedModelViewSet):
    """数据看板-演练数据"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    def list(self, request, *args, **kwargs):
        user = self.request.user
        total_exercises = Exercise.objects.filter(created_by=user).count()

        # 统计进行中的演练数量
        running_exercises = Exercise.objects.filter(status="RU", created_by=user).count()

        # 统计已感染的设备数量
        infected_devices = Device.objects.filter(infection_count__gt=0, exercise__created_by=user).count()

        # 统计总设备数量，防止除零错误
        total_devices = Device.objects.filter(exercise__created_by=user).count()
        infection_rate = (infected_devices / total_devices * 100) if total_devices > 0 else 0

        return Response({
            "total_exercises": total_exercises,
            "running_exercises": running_exercises,
            "infected_devices": infected_devices,
            "infection_rate": round(infection_rate, 2)  # 保留两位小数
        })


class ExercisesTrendViewSet(mixins.ListModelMixin,
                            GenericViewSet):
    """数据看板-场次"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    def get_trend_data(self, days):
        """计算过去 `days` 天的演练趋势和时间线"""
        today = timezone.now()
        start_date = today - timezone.timedelta(days=days)

        # 统计总场次
        count = Exercise.objects.filter(start_time__gte=start_date, created_by=self.request.user).count()

        # 统计每日演练数量
        timeline = []
        for i in range(days):
            day = today - timezone.timedelta(days=i)
            daily_count = Exercise.objects.filter(start_time__date=day, created_by=self.request.user).count()
            if daily_count > 0:  # 只记录有数据的天数
                timeline.append({"time": day.strftime("%Y-%m-%d"), "count": daily_count})

        return {"count": count, "timeline": timeline[::-1]}  # 逆序，保证时间从旧到新

    def list(self, request, *args, **kwargs):
        return Response({
            "last_7_days": self.get_trend_data(7),
            "last_14_days": self.get_trend_data(14),
            "last_30_days": self.get_trend_data(30),
        })


class AssetGroupStatisticsView(mixins.ListModelMixin,
                               GenericViewSet):
    """数据看板-资产统计"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    def list(self, request, *args, **kwargs):
        asset_groups = AssetGroup.objects.all().filter(created_by=self.request.user).annotate(
            total_assets=Count("asset"),
        ).values("id", "name", "total_assets")

        # 计算已感染的设备数
        for group in asset_groups:
            infected_count = Device.objects.filter(
                hostname__in=Asset.objects.filter(
                    group_id=group["id"],
                    created_by=self.request.user
                ).values_list("name", flat=True),
                infection_count__gt=0
            ).count()

            group["infected_assets"] = infected_count

        return Response({"groups": list(asset_groups)})


class ExportReportView(APIView):
    permission_classes = [permissions_utils.SelfAccessPolicy]

    def get(self, request, *args, **kwargs):
        # 获取 exercise_id 参数（如果有的话）
        import pdfkit
        exercise_id = kwargs.get('exercise_id', None)
        exercise = get_object_or_404(Exercise, pk=exercise_id)
        # 模板渲染
        template = Template(constants.HTML_TEMPLATE)
        context = {  # NOQA
            'name': '红色警戒',
            'drill_time': '2025-06-10 17:39:26 - 2025-06-10 17:42:29',
            'duration': '3分钟',
            'company_name': '网安演练中心',
            'status': '勒索病毒A',
            'virus_name': "仿生病毒",
            'devices_count': 10,
            'infected_devices': 12,
            'email_sent': 80,
            'email_clicked': 22,
            'email_rate': '27.5%',
            'capture_data': [
                {'email': '<EMAIL>', 'send_time': '17:39:26', 'click_time': '17:39:50', 'submit_time': '17:40:01',
                 'device_info': 'Win10-Chrome', 'status': '已提交'},
            ],
            'assets': [
                {'group': '办公组', 'name': 'PC-01', 'type': '电脑', 'status': '在线', 'ip': '***********'},
            ],
            'mail_logs': [
                {'receiver': '<EMAIL>', 'subject': '关于XX培训', 'send_time': '2025-06-10 17:39:26',
                 'click_status': '已点击'},
            ]
        }
        context['name'] = exercise.name
        context['drill_time'] = f"{exercise.start_time}-{exercise.end_time}"
        # 持续时长
        duration = exercise.end_time - exercise.start_time
        days = duration.days
        hours, remainder = divmod(duration.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        drill_duration = f"{days} 天 {hours} 小时 {minutes} 分 {seconds} 秒"
        context['duration'] = drill_duration
        # 演练单位
        context['company_name'] = exercise.negotiation.company_name
        # 演练状态
        context['status'] = exercise.get_status_display()
        # 病毒名称
        context['virus_name'] = exercise.virus.name
        # 目标设备总数
        context['devices_count'] = (
            Asset.objects.filter(group__in=exercise.target_groups.all())
            .filter(asset_type__in=['EP', 'SV', 'NW']).count()
        )
        # 已感染设备数
        infection_records = InfectionRecord.objects.filter(exercise=exercise).count()
        context['infected_devices'] = infection_records
        # 邮件发送总数
        email_logs = EmailLog.objects.filter(exercise=exercise)
        email_sent = email_logs.count()
        context['email_sent'] = email_sent
        # 邮件点击数
        email_clicked = EmailLog.objects.filter(exercise=exercise, status='SUCCESS').count()
        context['email_clicked'] = email_clicked
        # 成功率
        email_rate = f"{round(email_clicked / email_sent * 100, 2)}%"
        context['email_rate'] = email_rate
        # 捕获数据
        email_logs = EmailLog.objects.filter(exercise=exercise).prefetch_related("form_submits")
        capture_data = []
        for email in email_logs:
            click_time = ""
            submit_time = ""
            device_info = ""
            status_ = ""
            form_data = email.form_submits.all().order_by("created_at")
            row_data = []
            for form in form_data:
                if form.status == "CLICK":
                    click_time = form.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    status_ = "已点击"
                if form.status == "SUBMIT":
                    submit_time = form.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    status_ = "已提交"
                    device_info = form.browser_info
                row_data.append({
                    "status": form.status,
                    "datetime": form.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "data": form.for_data,
                    "is_click": email.is_click,
                    "browser_info": form.browser_info,
                    "os_info": form.os_info,
                })
            capture_data.append({
                "id": email.id,
                "exercise_create_time": email.exercise.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "send_email_time": email.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "email": email.recipient,
                "is_click": email.is_click,
                "click_time": click_time,
                "submit_time": submit_time,
                "device_info": device_info,
                "status": status_,
                "timeline": row_data
            })
        context['capture_data'] = capture_data
        # 资产信息
        assets_obj = (Asset.objects.filter(group__in=exercise.target_groups.all())
                      .filter(asset_type__in=['EP', 'SV', 'NW']))
        assets = []
        for asset in assets_obj:
            is_infection = "未感染"
            device = Device.objects.filter(hostname=asset.name, exercise_id=exercise_id).order_by('-created_at').first()
            if device and device.infection_count > 0:
                is_infection = "已感染"
            data = {
                "group": asset.group.name,
                "name": asset.name,
                "is_infection": is_infection,
                "asset_type": asset.get_asset_type_display(),
                "ip_address": asset.ip_address_v4 or '',
            }
            assets.append(data)
        context['assets'] = assets
        # 邮件发送记录
        mail_logs = []
        for row in email_logs:
            mail_logs.append({
                "recipient": row.recipient,
                "email_subject": row.email_task.email_template.email_subject,
                "send_time": row.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "is_click": '已点击' if row.is_click else '未点击',
            })
        context['mail_logs'] = mail_logs

        html_out = template.render(context)  # 如果有上下文，可以传入相应的变量

        # wkhtmltopdf 配置路径
        path_wkhtmltopdf = constants.WKHTMLTOPDF_PATH
        config = pdfkit.configuration(wkhtmltopdf=path_wkhtmltopdf)

        # 使用 pdfkit 将 HTML 转换为 PDF
        options = {
            'encoding': 'UTF-8',
            'no-images': None,
            'disable-javascript': None
        }
        pdf_output = pdfkit.from_string(html_out, False, configuration=config, options=options)

        # 设置响应类型为 PDF 文件
        response = HttpResponse(pdf_output, content_type='application/pdf')

        # 设置下载文件名
        quoted_filename = parse.quote(exercise.name)
        response['Content-Disposition'] = f'attachment; filename="{quoted_filename}.pdf"'

        return response


class ExportReportViewDoc(APIView):
    permission_classes = [permissions_utils.SelfAccessPolicy]

    def set_title_text(self, doc, title, bold=False, size=12):
        """设置标题样式"""
        title.style = doc.styles['Title']

        # 强制设置标题字体为宋体
        run = title.runs[0]
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')


    def get(self, request, *args, **kwargs):
        exercise_id = kwargs.get('exercise_id', None)
        exercise = get_object_or_404(Exercise, pk=exercise_id)
        # 创建 Word 文档
        doc = Document()

        # 设置全局默认字体
        style = doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(12)

        # 添加标题
        title_1 = doc.add_heading('网络安全演练报告', 0)
        self.set_title_text(doc, title_1)
        duration = exercise.end_time - exercise.start_time
        days = duration.days
        hours, remainder = divmod(duration.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        drill_duration = f"{days} 天 {hours} 小时 {minutes} 分 {seconds} 秒"

        # 添加段落内容（可动态替换为你的数据）
        doc.add_paragraph(f'演练名称：{exercise.name}')
        doc.add_paragraph(f'演练时间：{exercise.start_time}-{exercise.end_time}')
        doc.add_paragraph(f'持续时间：{drill_duration}')
        doc.add_paragraph(f'演练单位：{exercise.negotiation.company_name}')

        # 第二部分：捕获数据
        title_2 = doc.add_heading('捕获数据', level=1)
        self.set_title_text(doc, title_2)
        # 添加表头（主表）
        main_table = doc.add_table(rows=1, cols=6)
        main_table.style = 'Table Grid'
        hdr_cells = main_table.rows[0].cells
        hdr_cells[0].text = 'Email'
        hdr_cells[1].text = '发送时间'
        hdr_cells[2].text = '点击时间'
        hdr_cells[3].text = '提交时间'
        hdr_cells[4].text = '设备信息'
        hdr_cells[5].text = '状态'

        email_logs = EmailLog.objects.filter(exercise=exercise).prefetch_related("form_submits")
        data1 = []
        for email in email_logs:
            click_time = ""
            submit_time = ""
            device_info = ""
            status_ = ""
            form_data = email.form_submits.all().order_by("created_at")
            row_data = []
            for form in form_data:
                if form.status == "CLICK":
                    click_time = form.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    status_ = "已点击"
                if form.status == "SUBMIT":
                    submit_time = form.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    status_ = "已提交"
                    device_info = form.browser_info
                if not form.for_data:
                    continue
                row_data.append(form.for_data)
            data1.append({
                "id": email.id,
                "exercise_create_time": email.exercise.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "send_time": email.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "email": email.recipient,
                "is_click": email.is_click,
                "click_time": click_time,
                "submit_time": submit_time,
                "device": device_info,
                "status": status_,
                "params": row_data
            })
        # 示例数据
        # data1 = [
        #     {
        #         "email": "<EMAIL>",
        #         "send_time": "2025-03-19 15:18:57",
        #         "click_time": "2025-03-19 15:19:53",
        #         "submit_time": "2025-03-19 15:23:00",
        #         "device": "Mobile Safari (Version: 18.3.1)",
        #         "status": "已提交",
        #         "params": [
        #             {"key": "password", "value": "woshinibaba"},
        #             {"key": "username", "value": "雪辰"}
        #         ]
        #     },
        #     {
        #         "email": "<EMAIL>",
        #         "send_time": "2025-03-19 15:18:57",
        #         "click_time": "2025-03-19 15:19:53",
        #         "submit_time": "2025-03-19 15:23:00",
        #         "device": "Mobile Safari (Version: 18.3.1)",
        #         "status": "已提交",
        #         "params": [
        #         ]
        #     },
        #     {
        #         "email": "<EMAIL>",
        #         "send_time": "2025-03-19 15:18:57",
        #         "click_time": "2025-03-19 15:19:53",
        #         "submit_time": "2025-03-19 15:23:00",
        #         "device": "Mobile Safari (Version: 18.3.1)",
        #         "status": "已提交",
        #         "params": [
        #         ]
        #     }
        # ]

        for item in data1:
            # 第1行：主表数据行
            data_row = main_table.add_row().cells
            data_row[0].text = item['email']
            data_row[1].text = item['send_time']
            data_row[2].text = item['click_time']
            data_row[3].text = item['submit_time']
            data_row[4].text = item['device']
            data_row[5].text = item['status']

            # 判断是否有参数，只有在有参数时添加嵌套表格行
            if item.get('params'):
                # 第2行：嵌套参数表，整行只有一个 cell，横跨 6 列
                param_row = main_table.add_row().cells
                merged_cell = param_row[0]
                for i in range(1, 6):
                    merged_cell = merged_cell.merge(param_row[i])
                # 清除默认段落，避免嵌套表格前多出空格
                for p in merged_cell.paragraphs:
                    p._element.getparent().remove(p._element)
                # 在合并后的 cell 中添加参数表
                nested_table = merged_cell.add_table(rows=1, cols=2)
                nested_table.style = 'Table Grid'
                nh = nested_table.rows[0].cells
                nh[0].text = '参数名'
                nh[1].text = '参数值'

                for each in item['params']:
                    for k, v in each.items():
                        prow = nested_table.add_row().cells
                        prow[0].text = k
                        prow[1].text = v

        # 第三部分: 目标资产信息
        title_3 = doc.add_heading('目标资产信息', level=1)
        self.set_title_text(doc, title_3)
        table2 = doc.add_table(rows=1, cols=5)
        table2.style = 'Table Grid'
        hdr_cells2 = table2.rows[0].cells
        hdr_cells2[0].text = '所属组'
        hdr_cells2[1].text = '资产名称'
        hdr_cells2[2].text = '资产类型'
        hdr_cells2[3].text = '状态'
        hdr_cells2[4].text = 'IP地址'

        # 示例数据
        data2 = [
            ['DESKTOP-9PK929R', '终端', '已感染', '***********', '', ''],
            ['zzh', '终端', '已感染', '']
        ]
        assets_obj = (Asset.objects.filter(group__in=exercise.target_groups.all())
                      .filter(asset_type__in=['EP', 'SV', 'NW']))
        data2 = []
        for asset in assets_obj:
            is_infection = "未感染"
            device = Device.objects.filter(hostname=asset.name, exercise_id=exercise_id).order_by('-created_at').first()
            if device and device.infection_count > 0:
                is_infection = "已感染"
            data2.append([asset.group.name or '', asset.name or '', is_infection, asset.get_asset_type_display() or '',
                          asset.ip_address_v4 or ''])
        for row in data2:
            cells = table2.add_row().cells
            for i in range(4):
                cells[i].text = row[i]

        # 第四部分: 邮件发送记录
        title_4 = doc.add_heading('邮件发送记录', level=1)
        self.set_title_text(doc, title_4)
        table3 = doc.add_table(rows=1, cols=4)
        table3.style = 'Table Grid'
        hdr_cells3 = table3.rows[0].cells
        hdr_cells3[0].text = '收件人'
        hdr_cells3[1].text = '邮件主题'
        hdr_cells3[2].text = '发送时间'
        hdr_cells3[3].text = '点击状态'

        # 示例数据
        data3 = [
            ['DESKTOP-9PK929R', '终端', '已感染', '***********'],
            ['zzh', '终端', '已感染', '']
        ]
        data3 = []
        for row in email_logs:
            data3.append([row.recipient, row.email_task.email_template.email_subject,
                          row.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                          '已点击' if row.is_click else '未点击'])
        for row in data3:
            cells = table3.add_row().cells
            for i in range(4):
                cells[i].text = row[i]

        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)

        # 构造响应，提供下载
        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        quoted_filename = parse.quote(exercise.name)
        response['Content-Disposition'] = f'attachment; filename="{quoted_filename}.docx"'
        return response
